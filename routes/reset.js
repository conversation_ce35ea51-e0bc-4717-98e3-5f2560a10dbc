import { exec } from 'child_process';
import crypto from 'crypto';
import express from 'express';
import fs from 'fs-extra';
import os from 'os';
import path from 'path';
import { open } from 'sqlite';
import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

const rt = express.Router();
const execPromise = promisify(exec);

const gp = () => {
  const pt = os.platform();
  const hm = os.homedir();
  const dc = path.join(hm, 'Documents', '.cursor-free-vip');
  const cf = path.join(dc, 'config.ini');
  let mp = '';
  let sp = '';
  let dp = '';
  let ap = '';
  let cp = '';
  let up = '';

  // Caminhos para Cursor 1.0+ (estrutura atualizada)
  if (pt === 'win32') {
    mp = path.join(hm, 'AppData', 'Roaming', 'Cursor', 'machineId');
    sp = path.join(hm, 'AppData', 'Roaming', 'Cursor', 'User', 'globalStorage', 'storage.json');
    dp = path.join(hm, 'AppData', 'Roaming', 'Cursor', 'User', 'globalStorage', 'state.vscdb');
    ap = path.join(hm, 'AppData', 'Local', 'Programs', 'Cursor', 'resources', 'app');
    cp = path.join(hm, 'AppData', 'Roaming', 'Cursor', 'User', 'globalStorage', 'cursor.json');
    up = path.join(hm, 'AppData', 'Local', 'Programs', 'Cursor', 'resources', 'app-update.yml');
  } else if (pt === 'darwin') {
    mp = path.join(hm, 'Library', 'Application Support', 'Cursor', 'machineId');
    sp = path.join(hm, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'storage.json');
    dp = path.join(hm, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'state.vscdb');
    ap = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app');
    cp = path.join(hm, 'Library', 'Application Support', 'Cursor', 'User', 'globalStorage', 'cursor.json');
    up = path.join('/Applications', 'Cursor.app', 'Contents', 'Resources', 'app-update.yml');
  } else if (pt === 'linux') {
    mp = path.join(hm, '.config', 'cursor', 'machineId');
    sp = path.join(hm, '.config', 'cursor', 'User', 'globalStorage', 'storage.json');
    dp = path.join(hm, '.config', 'cursor', 'User', 'globalStorage', 'state.vscdb');
    ap = path.join('/usr', 'share', 'cursor', 'resources', 'app');
    cp = path.join(hm, '.config', 'cursor', 'User', 'globalStorage', 'cursor.json');
    up = path.join('/usr', 'share', 'cursor', 'resources', 'app-update.yml');
  }

  return { mp, sp, dp, ap, cp, up, pt, dc, cf };
};

// Nova função para caminhos do Augment Code
const getAugmentPaths = () => {
  const pt = os.platform();
  const hm = os.homedir();
  let augmentPaths = [];

  if (pt === 'win32') {
    augmentPaths = [
      path.join(hm, 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'augment.vscode-augment'),
      path.join(hm, 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage'),
      path.join(hm, 'AppData', 'Roaming', 'Code', 'CachedExtensionVSIXs'),
      path.join(hm, 'AppData', 'Roaming', 'Code', 'logs')
    ];
  } else if (pt === 'darwin') {
    augmentPaths = [
      path.join(hm, 'Library', 'Application Support', 'Code', 'User', 'globalStorage', 'augment.vscode-augment'),
      path.join(hm, 'Library', 'Application Support', 'Code', 'User', 'workspaceStorage'),
      path.join(hm, 'Library', 'Application Support', 'Code', 'CachedExtensionVSIXs'),
      path.join(hm, 'Library', 'Application Support', 'Code', 'logs'),
      path.join(hm, 'Library', 'Application Support', 'Arc', 'User Data', 'Profile 1', 'IndexedDB', 'https_auth.augmentcode.com_0.indexeddb.leveldb'),
      path.join(hm, 'Library', 'Application Support', 'Arc', 'User Data', 'Profile 1', 'IndexedDB', 'https_login.augmentcode.com_0.indexeddb.leveldb')
    ];
  } else if (pt === 'linux') {
    augmentPaths = [
      path.join(hm, '.config', 'Code', 'User', 'globalStorage', 'augment.vscode-augment'),
      path.join(hm, '.config', 'Code', 'User', 'workspaceStorage'),
      path.join(hm, '.config', 'Code', 'CachedExtensionVSIXs'),
      path.join(hm, '.config', 'Code', 'logs')
    ];
  }

  return { augmentPaths, pt, hm };
};

const mk = () => {
  const dt = new Date();
  const yr = dt.getFullYear();
  const mn = String(dt.getMonth() + 1).padStart(2, '0');
  const dy = String(dt.getDate()).padStart(2, '0');
  const hr = String(dt.getHours()).padStart(2, '0');
  const mi = String(dt.getMinutes()).padStart(2, '0');
  const sc = String(dt.getSeconds()).padStart(2, '0');
  return `${yr}${mn}${dy}_${hr}${mi}${sc}`;
};

const bk = async (filePath) => {
  try {
    const bkPath = `${filePath}.bak.${mk()}`;
    await fs.copy(filePath, bkPath);
    return bkPath;
  } catch (error) {
    return null;
  }
};

const gh = () => {
  return crypto.randomBytes(16).toString('hex');
};

const gm = () => {
  return uuidv4().toUpperCase();
};

const cs = (seed) => {
  return crypto.createHash('sha256').update(seed).digest('hex');
};

const kc = async () => {
  if (os.platform() !== 'darwin') return false;

  try {
    await execPromise('security delete-generic-password -s "Cursor" -a "token" 2>/dev/null');
    await execPromise('security delete-generic-password -s "Cursor" -a "refreshToken" 2>/dev/null');
    await execPromise('security delete-generic-password -s "Cursor" -a "authToken" 2>/dev/null');
    await execPromise('security delete-generic-password -s "Cursor" -a "sessionToken" 2>/dev/null');
    await execPromise('security delete-generic-password -s "Cursor" -a "accessToken" 2>/dev/null');
    await execPromise('security delete-generic-password -s "com.todesktop.230313mzl4w4u92" 2>/dev/null');
    return true;
  } catch (e) {
    return false;
  }
};

const cb = async () => {
  const logs = [];
  const { pt } = gp();

  try {
    logs.push("🔄 Clearing browser data and cookies...");

    if (pt === 'win32') {
      // Limpar dados do Chromium no Windows
      const chromiumPaths = [
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'Cookies'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'Local Storage'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'Session Storage'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'IndexedDB'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'Web Data'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data', 'Default', 'Preferences')
      ];

      for (const browserPath of chromiumPaths) {
        try {
          if (fs.existsSync(browserPath)) {
            await fs.rm(browserPath, { recursive: true, force: true });
            logs.push(`✅ Cleared: ${path.basename(browserPath)}`);
          }
        } catch (e) {
          logs.push(`⚠️ Could not clear ${path.basename(browserPath)}: ${e.message}`);
        }
      }
    } else if (pt === 'darwin') {
      // Limpar dados do Chromium no macOS
      const chromiumPaths = [
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'Cookies'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'Local Storage'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'Session Storage'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'IndexedDB'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'Web Data'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'Default', 'Preferences')
      ];

      for (const browserPath of chromiumPaths) {
        try {
          if (fs.existsSync(browserPath)) {
            await fs.rm(browserPath, { recursive: true, force: true });
            logs.push(`✅ Cleared: ${path.basename(browserPath)}`);
          }
        } catch (e) {
          logs.push(`⚠️ Could not clear ${path.basename(browserPath)}: ${e.message}`);
        }
      }
    }

    return logs;
  } catch (err) {
    logs.push(`❌ Browser data cleanup error: ${err.message}`);
    return logs;
  }
};

// Nova função para resetar dados do Augment Code
const resetAugmentData = async () => {
  const logs = [];
  const { augmentPaths, pt, hm } = getAugmentPaths();

  try {
    logs.push("🔄 Starting Augment Code data reset...");

    // Terminar processos do VS Code que podem estar usando Augment
    try {
      if (pt === 'win32') {
        await execPromise('taskkill /F /IM Code.exe /T 2>nul');
      } else {
        await execPromise('pkill -f "Visual Studio Code" 2>/dev/null');
        await execPromise('pkill -f "Code" 2>/dev/null');
      }
      logs.push("✅ VS Code processes terminated");
    } catch (e) {
      logs.push("ℹ️ No VS Code processes found");
    }

    // Limpar todos os dados do Augment
    for (const augmentPath of augmentPaths) {
      try {
        if (fs.existsSync(augmentPath)) {
          await fs.rm(augmentPath, { recursive: true, force: true });
          logs.push(`✅ Cleared: ${path.basename(augmentPath)}`);
        }
      } catch (e) {
        logs.push(`⚠️ Could not clear ${path.basename(augmentPath)}: ${e.message}`);
      }
    }

    // Limpar workspaceStorage específico do Augment
    const workspaceStoragePath = path.join(hm, 'Library', 'Application Support', 'Code', 'User', 'workspaceStorage');
    if (fs.existsSync(workspaceStoragePath)) {
      try {
        const workspaces = await fs.readdir(workspaceStoragePath);
        for (const workspace of workspaces) {
          const augmentWorkspacePath = path.join(workspaceStoragePath, workspace, 'Augment.vscode-augment');
          if (fs.existsSync(augmentWorkspacePath)) {
            await fs.rm(augmentWorkspacePath, { recursive: true, force: true });
            logs.push(`✅ Cleared workspace: ${workspace.substring(0, 8)}...`);
          }
        }
      } catch (e) {
        logs.push(`⚠️ Error clearing workspaces: ${e.message}`);
      }
    }

    // Limpar dados do navegador relacionados ao Augment
    if (pt === 'darwin') {
      const browserPaths = [
        path.join(hm, 'Library', 'Application Support', 'Arc', 'User Data'),
        path.join(hm, 'Library', 'Application Support', 'Google', 'Chrome', 'Default'),
        path.join(hm, 'Library', 'Application Support', 'Microsoft Edge', 'Default')
      ];

      for (const browserPath of browserPaths) {
        try {
          const indexedDBPath = path.join(browserPath, 'IndexedDB');
          if (fs.existsSync(indexedDBPath)) {
            const dbs = await fs.readdir(indexedDBPath);
            for (const db of dbs) {
              if (db.includes('augmentcode.com')) {
                await fs.rm(path.join(indexedDBPath, db), { recursive: true, force: true });
                logs.push(`✅ Cleared browser data: ${db}`);
              }
            }
          }
        } catch (e) {
          logs.push(`⚠️ Could not clear browser data: ${e.message}`);
        }
      }
    }

    // Limpar keychain do macOS (dados de autenticação do Augment)
    if (pt === 'darwin') {
      try {
        await execPromise('security delete-generic-password -s "augmentcode.com" 2>/dev/null');
        await execPromise('security delete-generic-password -s "auth.augmentcode.com" 2>/dev/null');
        await execPromise('security delete-generic-password -s "login.augmentcode.com" 2>/dev/null');
        logs.push("✅ Augment keychain data cleared");
      } catch (e) {
        logs.push("ℹ️ No Augment keychain data found");
      }
    }

    logs.push("✅ Augment Code data reset completed");
    return logs;

  } catch (err) {
    logs.push(`❌ Augment reset error: ${err.message}`);
    return logs;
  }
};

const wu = async (newGuid) => {
  if (os.platform() !== 'win32') return false;
  
  try {
    const cmds = [
      `REG ADD HKCU\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid /t REG_SZ /d ${uuidv4()} /f`,
      `REG ADD HKCU\\SOFTWARE\\Microsoft\\SQMClient /v MachineId /t REG_SZ /d ${newGuid} /f`,
      `REG ADD HKLM\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid /t REG_SZ /d ${uuidv4()} /f`,
      `REG ADD HKLM\\SOFTWARE\\Microsoft\\SQMClient /v MachineId /t REG_SZ /d ${newGuid} /f`,
      `REG ADD HKCU\\Software\\Cursor /v MachineId /t REG_SZ /d ${newGuid} /f /reg:64`
    ];
    
    for (const cmd of cmds) {
      try {
        await execPromise(cmd);
      } catch (e) {}
    }
    return true;
  } catch (e) {
    return false;
  }
};

const um = async (id) => {
  if (os.platform() !== 'darwin') return false;
  
  try {
    const p = '/Library/Preferences/SystemConfiguration/com.apple.platform.uuid.plist';
    const hp = path.join(os.homedir(), p);
    
    try {
      await execPromise(`defaults write ${hp} "UUID" "${id}"`);
      await execPromise(`sudo defaults write ${p} "UUID" "${id}"`);
      return true;
    } catch (e) {}

    return false;
  } catch (e) {
    return false;
  }
};

const rm = async () => {
  const logs = [];
  const { mp, sp, dp, ap, cp, pt, dc, cf } = gp();

  try {
    logs.push("🔄 Starting Enhanced Machine ID Reset...");

    // Primeiro, vamos terminar todos os processos do Cursor
    logs.push("🔄 Terminating Cursor processes...");
    try {
      if (pt === 'win32') {
        await execPromise('taskkill /F /IM Cursor.exe /T 2>nul');
      } else {
        await execPromise('pkill -f Cursor 2>/dev/null');
      }
      logs.push("✅ Cursor processes terminated");
    } catch (e) {
      logs.push("ℹ️ No Cursor processes found");
    }

    logs.push("ℹ️ Checking Config File...");

    if (!fs.existsSync(dc)) {
      await fs.ensureDir(dc);
      logs.push("ℹ️ Created config directory");
    }

    logs.push("📄 Reading Current Config...");

    if (!fs.existsSync(sp)) {
      logs.push("⚠️ Warning: Storage file not found, will create if needed");
    }

    if (fs.existsSync(sp)) {
      const bkPath = await bk(sp);
      logs.push(`💾 Creating Config Backup: ${bkPath}`);
    }
    
    logs.push("🔄 Generating New Machine ID...");

    const newGuid = `{${gm().replace(/-/g, '-').toUpperCase()}}`;
    const machId = uuidv4();
    const deviceId = uuidv4();
    const sqmId = newGuid;
    const macId = crypto.randomBytes(64).toString('hex');
    
    logs.push("📄 Saving New Config to JSON...");
    
    if (fs.existsSync(sp)) {
      try {
        const storageData = JSON.parse(await fs.readFile(sp, 'utf8'));
        storageData['update.mode'] = 'none';
        storageData['serviceMachineId'] = deviceId;
        storageData['telemetry.devDeviceId'] = deviceId;
        storageData['telemetry.macMachineId'] = macId;
        storageData['telemetry.machineId'] = cs(machId);
        storageData['telemetry.sqmId'] = sqmId;
        await fs.writeFile(sp, JSON.stringify(storageData, null, 2));
      } catch (err) {
        const newStorageData = {
          'update.mode': 'none',
          'serviceMachineId': deviceId,
          'telemetry.devDeviceId': deviceId,
          'telemetry.macMachineId': macId,
          'telemetry.machineId': cs(machId),
          'telemetry.sqmId': sqmId
        };
        await fs.writeFile(sp, JSON.stringify(newStorageData, null, 2));
      }
    } else {
      const newStorageData = {
        'update.mode': 'none',
        'serviceMachineId': deviceId,
        'telemetry.devDeviceId': deviceId,
        'telemetry.macMachineId': macId,
        'telemetry.machineId': cs(machId),
        'telemetry.sqmId': sqmId
      };
      await fs.ensureDir(path.dirname(sp));
      await fs.writeFile(sp, JSON.stringify(newStorageData, null, 2));
    }
    
    logs.push("ℹ️ Updating SQLite Database...");
    
    const newIds = {
      "telemetry.devDeviceId": deviceId,
      "telemetry.macMachineId": macId,
      "telemetry.machineId": cs(machId),
      "telemetry.sqmId": sqmId,
      "storage.serviceMachineId": deviceId
    };

    if (fs.existsSync(dp)) {
      await bk(dp);
      
      try {
        const db = await open({
          filename: dp,
          driver: sqlite3.Database
        });
        
        await db.exec(`
          CREATE TABLE IF NOT EXISTS ItemTable (
            key TEXT PRIMARY KEY,
            value TEXT
          )
        `);
        
        for (const [key, value] of Object.entries(newIds)) {
          await db.run(`
            INSERT OR REPLACE INTO ItemTable (key, value) 
            VALUES (?, ?)
          `, [key, JSON.stringify(value)]);
          logs.push(`ℹ️ Updating Key-Value Pair: ${key}`);
        }
        
        // Limpeza abrangente do banco de dados para Cursor 1.0+
        await db.run(`UPDATE ItemTable SET value = '{"global":{"usage":{"sessionCount":0,"tokenCount":0}}}' WHERE key LIKE '%cursor%usage%'`);
        await db.run(`UPDATE ItemTable SET value = '"pro"' WHERE key LIKE '%cursor%tier%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%cursor.lastUpdateCheck%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%cursor.trialStartTime%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%cursor.trialEndTime%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%trial%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%account%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%auth%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%session%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%login%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%user%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%device%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%machine%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%fingerprint%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%identifier%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%limit%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%count%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%usage%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%quota%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%restriction%'`);

        // Limpeza específica para Cursor 1.0+
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%subscription%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%billing%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%payment%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%license%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%activation%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%verification%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%validation%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%token%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%jwt%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%bearer%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%refresh%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%access%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%credential%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%cookie%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%cache%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%history%'`);
        await db.run(`DELETE FROM ItemTable WHERE key LIKE '%recent%'`);

        // Inserir novos valores limpos para Cursor 1.0+
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.tier', '"pro"')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.usage', '{"global":{"usage":{"sessionCount":0,"tokenCount":0}}}')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.accountStatus', '"active"')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.trialStatus', '"completed"')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.subscriptionTier', '"pro"')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.planType', '"pro"')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.tokenLimit', '999999')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.maxTokens', '999999')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.remainingTokens', '999999')`);
        await db.run(`INSERT OR REPLACE INTO ItemTable (key, value) VALUES ('cursor.usedTokens', '0')`);

        logs.push("✅ Enhanced SQLite Database Cleanup for Cursor 1.0+ Completed");
        
        await db.close();
        logs.push("✅ SQLite Database Updated Successfully");
      } catch (err) {
        logs.push(`⚠️ SQLite Update Error: ${err.message}`);
      }
    } else {
      logs.push("⚠️ SQLite Database not found, skipping database updates");
    }
    
    logs.push("ℹ️ Updating System IDs...");
    
    if (fs.existsSync(mp)) {
      await bk(mp);
      await fs.writeFile(mp, machId);
      logs.push("✅ Machine ID File Updated");
    } else {
      await fs.ensureDir(path.dirname(mp));
      await fs.writeFile(mp, machId);
      logs.push("✅ Machine ID File Created");
    }
    
    if (fs.existsSync(cp)) {
      await bk(cp);
      try {
        const cursorData = JSON.parse(await fs.readFile(cp, 'utf8'));
        if (cursorData) {
          if (cursorData.global && cursorData.global.usage) {
            cursorData.global.usage.sessionCount = 0;
            cursorData.global.usage.tokenCount = 0;
          } else {
            cursorData.global = {
              usage: {
                sessionCount: 0,
                tokenCount: 0
              }
            };
          }
          cursorData.tier = "pro";
          await fs.writeFile(cp, JSON.stringify(cursorData, null, 2));
          logs.push("✅ Cursor.json Updated Successfully");
        }
      } catch (err) {
        logs.push(`⚠️ Cursor.json Update Error: ${err.message}`);
      }
    }
    
    // Limpeza de dados do navegador
    const browserLogs = await cb();
    logs.push(...browserLogs);

    // Limpeza adicional de arquivos e caches (Cursor 1.0+ específico)
    logs.push("🔄 Clearing additional Cursor 1.0+ caches and files...");

    const additionalPaths = [];
    if (pt === 'win32') {
      additionalPaths.push(
        // Caminhos tradicionais
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'logs'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'CachedData'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'CachedExtensions'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'workspaceStorage'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'User Data'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'logs'),
        // Novos caminhos para Cursor 1.0+
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'History'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'snippets'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'keybindings.json'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'settings.json'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'GPUCache'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'crashDumps'),
        path.join(os.homedir(), 'AppData', 'Local', 'Cursor', 'CrashDumps')
      );
    } else if (pt === 'darwin') {
      additionalPaths.push(
        // Caminhos tradicionais
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'logs'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'CachedData'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'CachedExtensions'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'workspaceStorage'),
        path.join(os.homedir(), 'Library', 'Caches', 'com.todesktop.230313mzl4w4u92'),
        path.join(os.homedir(), 'Library', 'Saved Application State', 'com.todesktop.230313mzl4w4u92.savedState'),
        // Novos caminhos para Cursor 1.0+ no macOS
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'History'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'snippets'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'keybindings.json'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'settings.json'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'GPUCache'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'crashDumps'),
        path.join(os.homedir(), 'Library', 'Caches', 'Cursor'),
        path.join(os.homedir(), 'Library', 'HTTPStorages', 'com.todesktop.230313mzl4w4u92'),
        path.join(os.homedir(), 'Library', 'WebKit', 'com.todesktop.230313mzl4w4u92')
      );
    } else if (pt === 'linux') {
      additionalPaths.push(
        // Caminhos tradicionais
        path.join(os.homedir(), '.config', 'cursor', 'logs'),
        path.join(os.homedir(), '.config', 'cursor', 'CachedData'),
        path.join(os.homedir(), '.config', 'cursor', 'CachedExtensions'),
        path.join(os.homedir(), '.config', 'cursor', 'User', 'workspaceStorage'),
        path.join(os.homedir(), '.cache', 'cursor'),
        // Novos caminhos para Cursor 1.0+ no Linux
        path.join(os.homedir(), '.config', 'cursor', 'User', 'History'),
        path.join(os.homedir(), '.config', 'cursor', 'User', 'snippets'),
        path.join(os.homedir(), '.config', 'cursor', 'User', 'keybindings.json'),
        path.join(os.homedir(), '.config', 'cursor', 'User', 'settings.json'),
        path.join(os.homedir(), '.config', 'cursor', 'GPUCache'),
        path.join(os.homedir(), '.config', 'cursor', 'crashDumps')
      );
    }

    for (const cachePath of additionalPaths) {
      try {
        if (fs.existsSync(cachePath)) {
          await fs.rm(cachePath, { recursive: true, force: true });
          logs.push(`✅ Cleared cache: ${path.basename(cachePath)}`);
        }
      } catch (e) {
        logs.push(`⚠️ Could not clear ${path.basename(cachePath)}: ${e.message}`);
      }
    }

    if (pt === 'win32') {
      try {
        const wr = await wu(newGuid);
        if (wr) {
          logs.push("✅ Windows Machine GUID Updated Successfully");
          logs.push(`ℹ️ New Machine ID: ${newGuid}`);
          logs.push("✅ Windows Machine ID Updated Successfully");
          }
        } catch (err) {
        logs.push(`⚠️ Windows Registry Update Error: ${err.message}`);
      }
    } else if (pt === 'darwin') {
      try {
        const mr = await um(macId);
        if (mr) {
          logs.push("✅ macOS Platform UUID Updated Successfully");
        }

        const kr = await kc();
        if (kr) {
          logs.push("✅ macOS Keychain Cleared Successfully");
        }

        // Limpeza adicional do macOS para Cursor 1.0+
        try {
          await execPromise('defaults delete com.todesktop.230313mzl4w4u92 2>/dev/null');
          await execPromise('defaults delete com.cursor.app 2>/dev/null');
          await execPromise('defaults delete com.cursor.desktop 2>/dev/null');
          logs.push("✅ macOS app preferences cleared");
        } catch (e) {}

        // Limpar cache de rede específico do Cursor 1.0+
        try {
          await execPromise('dscacheutil -flushcache 2>/dev/null');
          await execPromise('sudo dscacheutil -flushcache 2>/dev/null');
          logs.push("✅ macOS DNS cache cleared");
        } catch (e) {}

      } catch (err) {
        logs.push(`⚠️ macOS Update Error: ${err.message}`);
      }
    }
    
    logs.push("✅ Machine ID Reset Successfully");
    logs.push("\nℹ️ New IDs:");
    for (const [key, value] of Object.entries(newIds)) {
      logs.push(`ℹ️ ${key}: ${value}`);
    }

    logs.push("\n🔔 IMPORTANT NEXT STEPS FOR CURSOR 1.0+:");
    logs.push("1. ✅ Machine ID has been completely reset for Cursor 1.0.1");
    logs.push("2. 🔄 Restart Cursor completely (force quit if necessary)");
    logs.push("3. 🌐 If you still get the 'too many accounts' error:");
    logs.push("   - CRITICAL: Change your IP address (toggle mobile data or use VPN)");
    logs.push("   - Clear ALL browser cookies for cursor.sh and *.cursor.sh");
    logs.push("   - Try creating account in incognito/private mode");
    logs.push("   - Wait 5-10 minutes before trying again");
    logs.push("4. 📧 Use a completely fresh email address (not used before)");
    logs.push("5. 🔄 Run 'Bypass Token Limit' and 'Pro Conversion' after successful login");
    logs.push("6. 🚀 For Cursor 1.0+: Enhanced patterns applied for better compatibility");
    logs.push("\n⚠️ NOTE: Cursor 1.0+ has stronger detection - IP change is usually required!");

    return await ld(logs, "Enhanced Machine ID Reset");
  } catch (err) {
    logs.push(`❌ Process Error: ${err.message}`);
    return await ld(logs, "Machine ID Reset");
  }
};

const gw = () => {
  const { ap, pt } = gp();
  
  if (pt === 'win32') {
    return path.join(ap, 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
  } else if (pt === 'darwin') {
    return path.join(ap, 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
  } else if (pt === 'linux') {
    return path.join(ap, 'out', 'vs', 'workbench', 'workbench.desktop.main.js');
  }
  
  return '';
};

const bt = async () => {
  const logs = [];
  const { dp } = gp();
  const workbenchPath = gw();
  
  try {
    logs.push("ℹ️ Starting token limit bypass...");
    
    if (!fs.existsSync(workbenchPath)) {
      logs.push(`❌ Workbench file not found at: ${workbenchPath}`);
      return await ld(logs, "Bypass Token Limit");
    }
    
    const bkPath = await bk(workbenchPath);
    logs.push(`💾 Created backup: ${bkPath}`);
    
    const content = await fs.readFile(workbenchPath, 'utf8');
    
    // Padrões atualizados para Cursor 1.0+
    const patterns = {
      // Padrões tradicionais
      '<div>Pro Trial': '<div>Pro',
      'py-1">Auto-select': 'py-1">Bypass-Version-Pin',
      'async getEffectiveTokenLimit(e){const n=e.modelName;if(!n)return 2e5;': 'async getEffectiveTokenLimit(e){return 9000000;const n=e.modelName;if(!n)return 9e5;',
      'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>.");': 'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>. <h1>Pro</h1>");',
      'notifications-toasts': 'notifications-toasts hidden',
      'hasReachedTokenLimit\\(\\w+\\)\\s*{[^}]+}': 'hasReachedTokenLimit(e){return false}',
      'isProUser\\(\\w*\\)\\s*{\\s*[^}]+}': 'isProUser(){return true}',
      'isPro\\(\\w*\\)\\s*{\\s*[^}]+}': 'isPro(){return true}',
      'getTokenLimit\\(\\w*\\)\\s*{\\s*[^}]+}': 'getTokenLimit(){return 999999}',
      'getTokensRemaining\\(\\w*\\)\\s*{\\s*[^}]+}': 'getTokensRemaining(){return 999999}',
      'getTokensUsed\\(\\w*\\)\\s*{\\s*[^}]+}': 'getTokensUsed(){return 0}',

      // Novos padrões para Cursor 1.0+
      'tokenLimit:\\s*\\d+': 'tokenLimit: 999999',
      'maxTokens:\\s*\\d+': 'maxTokens: 999999',
      'remainingTokens:\\s*\\d+': 'remainingTokens: 999999',
      'usedTokens:\\s*\\d+': 'usedTokens: 0',
      'trialExpired:\\s*true': 'trialExpired: false',
      'subscriptionStatus:\\s*["\']trial["\']': 'subscriptionStatus: "pro"',
      'tier:\\s*["\']free["\']': 'tier: "pro"',
      'tier:\\s*["\']trial["\']': 'tier: "pro"',
      'planType:\\s*["\']free["\']': 'planType: "pro"',
      'planType:\\s*["\']trial["\']': 'planType: "pro"',
      'checkSubscription\\(\\w*\\)\\s*{\\s*[^}]+}': 'checkSubscription(){return {tier:"pro",active:true}}',
      'validateToken\\(\\w*\\)\\s*{\\s*[^}]+}': 'validateToken(){return true}',
      'checkTokenLimit\\(\\w*\\)\\s*{\\s*[^}]+}': 'checkTokenLimit(){return false}',
      'getSubscriptionTier\\(\\w*\\)\\s*{\\s*[^}]+}': 'getSubscriptionTier(){return "pro"}',
      'isSubscriptionActive\\(\\w*\\)\\s*{\\s*[^}]+}': 'isSubscriptionActive(){return true}'
    };
    
    let modified = content;
    for (const [pattern, replacement] of Object.entries(patterns)) {
      const regex = new RegExp(pattern, 'g');
      modified = modified.replace(regex, replacement);
    }
    
    await fs.writeFile(workbenchPath, modified);
    logs.push("✅ Workbench file modified successfully");
    
    if (fs.existsSync(dp)) {
      logs.push("ℹ️ Updating SQLite database for token limits...");
      await bk(dp);
      
      try {
    const db = await open({
      filename: dp,
      driver: sqlite3.Database
    });

    await db.run(`UPDATE ItemTable SET value = '{"global":{"usage":{"sessionCount":0,"tokenCount":0}}}' WHERE key LIKE '%cursor%usage%'`);
    await db.close();
        logs.push("✅ SQLite database updated for token limits");
      } catch (err) {
        logs.push(`⚠️ SQLite update error: ${err.message}`);
      }
    }
    
    logs.push("✅ Token limit bypass completed successfully");
    return await ld(logs, "Bypass Token Limit");
  } catch (err) {
    logs.push(`❌ Token limit bypass error: ${err.message}`);
    return await ld(logs, "Bypass Token Limit");
  }
};

const du = async () => {
  const { ap, pt, up } = gp();
  const logs = [];
  
  try {
    logs.push("ℹ️ Starting auto-update disabling process...");
    
    logs.push("🔄 Terminating any running Cursor processes...");
    try {
      if (pt === 'win32') {
        await execPromise('taskkill /F /IM Cursor.exe /T');
    } else {
        await execPromise('pkill -f Cursor');
      }
      logs.push("✅ Cursor processes terminated successfully");
    } catch (e) {
      logs.push("ℹ️ No running Cursor processes found");
    }
    
    let updaterPath;
    if (pt === 'win32') {
      updaterPath = path.join(os.homedir(), 'AppData', 'Local', 'cursor-updater');
    } else if (pt === 'darwin') {
      updaterPath = path.join(os.homedir(), 'Library', 'Application Support', 'cursor-updater');
    } else if (pt === 'linux') {
      updaterPath = path.join(os.homedir(), '.config', 'cursor-updater');
    }
    
    logs.push(`🔄 Removing updater directory: ${updaterPath}`);
    if (fs.existsSync(updaterPath)) {
    try {
        if (fs.statSync(updaterPath).isDirectory()) {
          await fs.rm(updaterPath, { recursive: true, force: true });
      } else {
          await fs.unlink(updaterPath);
        }
        logs.push("✅ Updater directory successfully removed");
      } catch (e) {
        logs.push(`⚠️ Updater directory is locked, skipping removal: ${e.message}`);
      }
    } else {
      logs.push("ℹ️ Updater directory not found, creating blocker file");
    }
    
    if (!up) {
      logs.push("⚠️ Update.yml path not found for this platform");
    } else {
      logs.push(`🔄 Clearing update.yml file: ${up}`);
      try {
        if (fs.existsSync(up)) {
          await bk(up);
          await fs.writeFile(up, '', 'utf8');
        logs.push("✅ Update.yml file successfully cleared");
    } else {
          logs.push("ℹ️ Update.yml file not found, creating new one");
          await fs.ensureDir(path.dirname(up));
        }
      } catch (e) {
        logs.push(`⚠️ Failed to clear update.yml file: ${e.message}`);
      }
    }
    
    logs.push("🔄 Creating blocker files to prevent auto-updates...");
    
    try {
      await fs.ensureDir(path.dirname(updaterPath));
      await fs.writeFile(updaterPath, '', 'utf8');
      
      if (pt === 'win32') {
        try {
          await execPromise(`attrib +r "${updaterPath}"`);
        } catch (e) {
          logs.push(`⚠️ Failed to set updater file as read-only: ${e.message}`);
        }
      } else {
        try {
          fs.chmodSync(updaterPath, 0o444);
        } catch (e) {
          logs.push(`⚠️ Failed to set updater file permissions: ${e.message}`);
        }
      }
      logs.push("✅ Updater blocker file created successfully");
    } catch (e) {
      logs.push(`⚠️ Failed to create updater blocker file: ${e.message}`);
    }
    
    if (up) {
      try {
        await fs.ensureDir(path.dirname(up));
        await fs.writeFile(up, '# This file is locked to prevent auto-updates\nversion: 0.0.0\n', 'utf8');
        
        if (pt === 'win32') {
          try {
            await execPromise(`attrib +r "${up}"`);
          } catch (e) {
            logs.push(`⚠️ Failed to set update.yml as read-only: ${e.message}`);
          }
        } else {
          try {
            fs.chmodSync(up, 0o444);
          } catch (e) {
            logs.push(`⚠️ Failed to set update.yml permissions: ${e.message}`);
          }
        }
        logs.push("✅ Update.yml blocker file created successfully");
      } catch (e) {
        logs.push(`⚠️ Failed to create update.yml blocker file: ${e.message}`);
      }
    }
    
    const pj = path.join(ap, 'product.json');
    if (fs.existsSync(pj)) {
      logs.push(`🔄 Modifying product.json to remove update URLs: ${pj}`);
      try {
        const bkPath = await bk(pj);
        logs.push(`💾 Created backup: ${bkPath}`);
        
        let content = await fs.readFile(pj, 'utf8');
        
        content = content.replace(/https:\/\/api2\.cursor\.sh\/aiserver\.v1\.AuthService\/DownloadUpdate/g, '')
          .replace(/https:\/\/api2\.cursor\.sh\/updates/g, '')
          .replace(/http:\/\/cursorapi\.com\/updates/g, '');
          
        await fs.writeFile(pj, content, 'utf8');
        logs.push("✅ Update URLs successfully removed from product.json");
      } catch (e) {
        logs.push(`⚠️ Failed to modify product.json: ${e.message}`);
      }
    } else {
      logs.push(`⚠️ Product.json not found at: ${pj}`);
    }
    
    logs.push("✅ Auto-updates successfully disabled");
    
    return await ld(logs, "Disable Auto-Update");
  } catch (e) {
    logs.push(`❌ Error disabling auto-updates: ${e.message}`);
    return await ld(logs, "Disable Auto-Update");
  }
};

const pc = async () => {
  const logs = [];
  const { dp } = gp();
  const workbenchPath = gw();
  
  try {
    logs.push("ℹ️ Starting Pro conversion...");
    
    if (fs.existsSync(dp)) {
      logs.push("ℹ️ Updating SQLite database for Pro features...");
      await bk(dp);
      
      try {
    const db = await open({
      filename: dp,
      driver: sqlite3.Database
    });

    await db.run(`UPDATE ItemTable SET value = '"pro"' WHERE key LIKE '%cursor%tier%'`);
    await db.close();
        logs.push("✅ Pro features enabled in SQLite database");
      } catch (err) {
        logs.push(`⚠️ SQLite update error: ${err.message}`);
      }
    } else {
      logs.push("⚠️ SQLite database not found, skipping database update");
    }
    
    if (fs.existsSync(workbenchPath)) {
      logs.push("ℹ️ Modifying workbench file for Pro UI...");
      const bkPath = await bk(workbenchPath);
      logs.push(`💾 Created backup: ${bkPath}`);
      
      const content = await fs.readFile(workbenchPath, 'utf8');
      
      const patterns = {
        '<div>Pro Trial': '<div>Pro',
        'Upgrade to Pro': 'Sazumi Github',
        'return t.pay': 'return function(){window.open("https://github.com/sazumivicky","_blank")}',
        'rocket': 'github',
        'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>.");': 'var DWr=ne("<div class=settings__item_description>You are currently signed in with <strong></strong>. <h1>Pro</h1>");',
      };
      
      let modified = content;
      for (const [pattern, replacement] of Object.entries(patterns)) {
        modified = modified.replace(new RegExp(pattern, 'g'), replacement);
      }
      
      await fs.writeFile(workbenchPath, modified);
      logs.push("✅ Workbench file modified for Pro UI");
    } else {
      logs.push(`⚠️ Workbench file not found at: ${workbenchPath}`);
    }
    
    logs.push("✅ Pro conversion completed successfully");
    return await ld(logs, "Pro Conversion + Custom UI");
  } catch (err) {
    logs.push(`❌ Pro conversion error: ${err.message}`);
    return await ld(logs, "Pro Conversion + Custom UI");
  }
};

const ld = async (logs, toolName) => {
  const hd = "==================================================";
  const t1 = `🔄 Cursor ${toolName} Tool`;
  
  let lg = [];
  lg.push(hd);
  lg.push(t1);
  lg.push(hd);
  
  logs.forEach(l => {
    lg.push(l);
  });
  
  return lg.join('\n');
};

rt.get('/reset', async (req, res) => {
  try {
    const result = await rm();
    res.json({ success: true, log: result });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Novo endpoint para resetar dados do Augment Code
rt.get('/reset-augment', async (req, res) => {
  try {
    const logs = await resetAugmentData();
    const result = await ld(logs, "Augment Code Reset");
    res.json({ success: true, log: result });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Endpoint combinado para resetar tanto Cursor quanto Augment
rt.get('/reset-all', async (req, res) => {
  try {
    const cursorResult = await rm();
    const augmentLogs = await resetAugmentData();

    const combinedLogs = [
      "🔄 Combined Reset: Cursor + Augment Code",
      "=" .repeat(50),
      "",
      "📱 CURSOR RESET:",
      cursorResult,
      "",
      "🔧 AUGMENT CODE RESET:",
      ...augmentLogs,
      "",
      "✅ Combined reset completed successfully!"
    ];

    const result = combinedLogs.join('\n');
    res.json({ success: true, log: result });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

rt.get('/patch', async (req, res) => {
  try {
    const action = req.query.action || 'bypass';
    let result;

    if (action === 'bypass') {
      result = await bt();
    } else if (action === 'disable') {
      result = await du();
    } else if (action === 'pro') {
      result = await pc();
    } else {
      result = await bt();
    }

    res.json({ success: true, log: result });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

rt.get('/paths', async (req, res) => {
  try {
    const { mp, sp, dp, ap, cp, up, pt, dc } = gp();
    
    let isRunning = false;
    try {
      if (pt === 'win32') {
        const { stdout } = await execPromise('tasklist /FI "IMAGENAME eq Cursor.exe" /NH');
        isRunning = stdout.includes('Cursor.exe');
      } else {
        const { stdout } = await execPromise('ps aux | grep -i Cursor | grep -v grep');
        isRunning = stdout.trim().length > 0;
      }
    } catch (e) {
      isRunning = false;
    }
    
    const info = {
      platform: pt,
      osVersion: os.release(),
      arch: os.arch(),
      homedir: os.homedir(),
      machinePath: mp,
      storagePath: sp,
      dbPath: dp,
      appPath: ap,
      cursorPath: cp,
      updatePath: up,
      isRunning,
      exists: {
        machineId: fs.existsSync(mp),
        storage: fs.existsSync(sp),
        database: fs.existsSync(dp),
        app: fs.existsSync(ap),
        cursor: fs.existsSync(cp),
        update: fs.existsSync(up)
      }
    };
    
    if (fs.existsSync(sp)) {
      try {
        const data = await fs.readFile(sp, 'utf8');
        const json = JSON.parse(data);
        info.storage = {
          machineId: json['telemetry.machineId'] || json['serviceMachineId'],
          devDeviceId: json['telemetry.devDeviceId'],
          tier: json['cursor.tier'] || 'unknown'
        };
      } catch (e) {}
    }
    
    if (fs.existsSync(dp)) {
      try {
        const db = await open({
          filename: dp,
          driver: sqlite3.Database
        });
        const rows = await db.all('SELECT key, value FROM ItemTable WHERE key LIKE "%cursor%" OR key LIKE "%telemetry%" LIMIT 10');
        info.database = rows.reduce((acc, row) => {
          try {
            acc[row.key] = JSON.parse(row.value);
          } catch (e) {
            acc[row.key] = row.value;
          }
          return acc;
        }, {});
        await db.close();
      } catch (e) {}
    }
  
  res.json(info);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

export default rt;