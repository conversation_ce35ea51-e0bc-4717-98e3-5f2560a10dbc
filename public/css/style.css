:root {
  --primary-color: #333333;
  --secondary-color: #f8f9fa;
  --accent-color: #757575;
  --accent-dark: #424242;
  --text-color: #212121;
  --success-color: #4caf50;
  --error-color: #f44336;
  --light-bg: #f1f3f4;
  --border-color: #e0e0e0;
  --border-radius: 6px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Google Sans', -apple-system, sans-serif;
  background-color: var(--secondary-color);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

header {
  text-align: center;
  margin-bottom: 2.5rem;
  padding: 1.5rem 0;
  position: relative;
}

header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--accent-color);
  border-radius: 3px;
}

header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  letter-spacing: -0.5px;
  font-weight: 700;
  text-transform: uppercase;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-dark));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
  display: inline-block;
}

header h1::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-dark));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  filter: blur(8px);
  opacity: 0.5;
}

header p {
  color: var(--primary-color);
  font-size: 1.1rem;
  opacity: 0.8;
}

main, #intro, #docs, #changelog, #requirements {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  main {
    grid-template-columns: repeat(2, 1fr);
  }
  
  main .card:last-child {
    grid-column: span 2;
  }
}

.card {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), border-color 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  border-color: var(--accent-color);
}

.card-header {
  background: white;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.card-header h2 {
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header h2 i {
  color: var(--accent-color);
  transition: transform 0.3s ease;
  font-size: 20px;
}

.card:hover .card-header h2 i {
  transform: scale(1.2);
}

.card-body {
  padding: 1.5rem;
}

.features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

@media (min-width: 768px) {
  .features {
    grid-template-columns: repeat(3, 1fr);
  }
}

.feature {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 1rem;
  background: var(--light-bg);
  border-radius: var(--border-radius);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid transparent;
}

.feature:hover {
  transform: translateY(-3px) scale(1.02);
  border-color: var(--accent-color);
  background: white;
}

.feature i {
  color: var(--accent-color);
  font-size: 24px;
  transition: transform 0.3s ease;
}

.feature:hover i {
  transform: scale(1.2);
}

.feature h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.feature p {
  font-size: 0.9rem;
  color: var(--primary-color);
  opacity: 0.8;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-dark));
  color: white;
  border: none;
  border-radius: 50px;
  padding: 0.75rem 1.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  letter-spacing: 0.25px;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: scale(0) translate(-50%, -50%);
  transform-origin: top left;
  transition: transform 0.6s;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn:hover::after {
  transform: scale(2.5) translate(-50%, -50%);
}

.btn:active {
  transform: translateY(1px);
}

.btn:disabled {
  background: #e0e0e0;
  color: #9e9e9e;
  cursor: not-allowed;
}

.btn:disabled::after {
  display: none;
}

.btn i {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: rotate(180deg);
}

.alert {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin: 1rem 0;
  font-size: 0.9rem;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background-color: var(--light-bg);
}

.alert-warning {
  background-color: #fff8e1;
  color: #bf360c;
}

.alert-info {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.alert i {
  font-size: 20px;
  color: var(--accent-color);
}

#reset-result {
  margin-top: 1.5rem;
  border-radius: var(--border-radius);
  font-size: 0.95rem;
}

.success, .error, .processing {
  padding: 1rem;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.success {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.error {
  background-color: #ffebee;
  color: #b71c1c;
}

.processing {
  background-color: #ede7f6;
  color: #311b92;
}

.success p, .error p, .processing p {
  display: flex;
  align-items: center;
  gap: 8px;
}

footer {
  text-align: center;
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  font-size: 0.9rem;
  color: var(--primary-color);
}

footer a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

footer a:hover {
  text-decoration: underline;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border-radius: var(--border-radius);
  overflow: hidden;
}

table, th, td {
  border: 1px solid var(--border-color);
}

th, td {
  padding: 0.75rem;
  text-align: left;
}

th {
  background-color: var(--light-bg);
  font-weight: 500;
  color: var(--primary-color);
}

tr:nth-child(even) {
  background-color: var(--light-bg);
}

.code-block {
  max-width: 100%;
  overflow-x: hidden;
  background: #1e1e1e;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.85rem;
  color: #d4d4d4;
  border: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) #1e1e1e;
  position: relative;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0;
}

.code-block::-webkit-scrollbar {
  height: 6px;
}

.code-block::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 3px;
}

.code-block::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: 3px;
}

.code-block::before, .code-block::after {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: 20px;
  pointer-events: none;
  z-index: 1;
  opacity: 0.7;
}

.code-block::before {
  left: 0;
  background: linear-gradient(to right, #1e1e1e, rgba(30, 30, 30, 0));
}

.code-block::after {
  right: 0;
  background: linear-gradient(to left, #1e1e1e, rgba(30, 30, 30, 0));
}

.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-running {
  background-color: var(--success-color);
  box-shadow: 0 0 5px var(--success-color);
  animation: pulse 1.5s infinite;
}

.status-stopped {
  background-color: var(--error-color);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  color: var(--primary-color);
}

.loading::before {
  content: "";
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: var(--accent-color);
  border-right-color: var(--accent-color);
  animation: sp 1s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  margin-right: 10px;
}

@keyframes sp {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.accordion {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.accordion-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.accordion-item:hover {
  border-color: var(--accent-color);
}

.accordion-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--light-bg);
  cursor: pointer;
  user-select: none;
}

.accordion-header i {
  transition: transform 0.3s ease;
  color: var(--accent-color);
  font-size: 18px;
}

.accordion-item.active .accordion-header i {
  transform: rotate(90deg);
}

.accordion-header h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.accordion-content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.accordion-item.active .accordion-content {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  opacity: 1;
}

.accordion-content p {
  margin-bottom: 1rem;
}

.accordion-content ul {
  padding-left: 1.5rem;
}

.accordion-content li {
  margin-bottom: 0.5rem;
}

.version {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  transition: transform 0.3s ease;
}

.version:hover {
  transform: translateX(5px);
}

.version:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.version-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.version-tag {
  font-weight: 600;
  color: var(--accent-color);
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.version:hover .version-tag {
  color: var(--accent-dark);
}

.version-date {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--accent-dark);
  transition: all 0.3s ease;
  margin-left: 10px;
  opacity: 0.8;
}

.version-changes {
  list-style-type: none;
}

.version-changes li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  transition: transform 0.3s ease;
}

.version-changes li:hover {
  transform: translateX(5px);
}

.change-type {
  display: inline-block;
  padding: 0.1rem 0.5rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
}

.new {
  background-color: #f1f3f4;
  color: #424242;
}

.improved {
  background-color: #e8f5e9;
  color: #388e3c;
}

.fixed {
  background-color: #ffebee;
  color: #d32f2f;
}

.added {
  background-color: #f5f5f5;
  color: #616161;
}

.removed {
  background-color: #e53935;
  color: white;
}

.req-list {
  list-style: none;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .req-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

.req-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0.75rem;
  background: var(--light-bg);
  border-radius: var(--border-radius);
  transition: transform 0.3s ease;
}

.req-list li:hover {
  transform: translateY(-3px);
  background: white;
  border: 1px solid var(--accent-color);
}

.req-list i {
  color: var(--accent-color);
  font-size: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

@media (min-width: 500px) {
  .action-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  gap: 8px;
  width: 100%;
}

@media (min-width: 500px) {
  .action-btn {
    width: auto;
    min-width: 150px;
  }
}

.github-btn {
  background: #24292e;
  color: white;
}

.github-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.donate-btn {
  background: linear-gradient(45deg, #9c27b0, #673ab7);
  color: white;
}

.donate-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-btn i {
  font-size: 18px;
}

@media (max-width: 480px) {
  header h1 {
    font-size: 1.8rem;
  }
  
  .card-body {
    padding: 1rem;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .version-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

.highlight {
  background-color: #f1f8e9;
  color: #558b2f;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.installation-steps {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.step {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.step-number {
  background: linear-gradient(45deg, var(--accent-color), var(--accent-dark));
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.step-content p {
  margin-top: 0.5rem;
}

#installation {
  margin-bottom: 3rem;
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  overflow: auto;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  margin: 5vh auto;
  width: 90%;
  max-width: 600px;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

@keyframes modalFadeIn {
  from {opacity: 0; transform: translateY(-30px);}
  to {opacity: 1; transform: translateY(0);}
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--accent-dark);
  margin: 0;
}

.modal-header h2 i {
  color: #f44336;
}

.modal-close {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s;
}

.modal-close:hover {
  color: #f44336;
  transform: scale(1.1);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
}

.modal-body p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.modal-body ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.modal-body li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 0.5rem;
}

.modal-body li::before {
  content: "•";
  color: var(--accent-color);
  font-weight: bold;
  position: absolute;
  left: -1rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.timeline {
  position: relative;
  margin: 0 0 0 20px;
  padding-left: 20px;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  padding-bottom: 15px;
}

.timeline-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -31px;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  border: 3px solid var(--accent-color);
  z-index: 1;
  transition: all 0.3s ease;
}

.timeline-item:hover .timeline-dot {
  transform: scale(1.2);
  background: var(--accent-color);
}

.timeline-date {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--accent-dark);
  transition: all 0.3s ease;
  margin-left: 10px;
  opacity: 0.8;
}

.timeline-item:hover .timeline-date {
  color: var(--accent-color);
  font-weight: 600;
}

@media (max-width: 768px) {
  .timeline {
    margin-left: 0;
  }
  
  .timeline-date {
    font-size: 0.8rem;
  }
  
  .version-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .timeline-date {
    margin-left: 0;
  }
}

.timeline-content {
  position: relative;
  margin-left: 15px;
}

.timeline-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 20px;
  margin-top: 15px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.timeline-item:hover .timeline-card {
  transform: translateY(-3px);
  border-color: var(--accent-color);
}

.version-tag {
  font-weight: 600;
  color: var(--accent-color);
  font-size: 1.1rem;
  transition: color 0.3s ease;
  background: rgba(117, 117, 117, 0.1);
  padding: 5px 12px;
  border-radius: 50px;
  display: inline-block;
}

.timeline-item:hover .version-tag {
  color: white;
  background: var(--accent-color);
}

.version-changes {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.version-changes li {
  margin-bottom: 0.75rem;
  padding-left: 0;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  transition: transform 0.3s ease;
}

.version-changes li:last-child {
  margin-bottom: 0;
}

.version-changes li:hover {
  transform: translateX(5px);
}

.change-type {
  display: inline-block;
  padding: 0.1rem 0.5rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .timeline {
    margin-left: 0;
  }
  
  .timeline-date {
    position: relative;
    left: 0;
    top: auto;
    margin-bottom: 10px;
    text-align: left;
    width: auto;
    font-size: 0.8rem;
    color: var(--accent-color);
  }
}

.log-output {
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: "Consolas", "Monaco", monospace;
  padding: 15px;
  border-radius: 5px;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 15px;
  border: 1px solid #333;
  font-size: 13px;
  line-height: 1.5;
  scrollbar-width: thin;
  scrollbar-color: #555 #1e1e1e;
}

.log-output::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.log-output::-webkit-scrollbar-track {
  background: #1e1e1e;
  border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.log-output::-webkit-scrollbar-thumb:hover {
  background: #777;
}

.result-table {
  width: 100%;
  margin-top: 1rem;
  border-collapse: collapse;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
}

.result-table th {
  text-align: left;
  background-color: #f5f5f5;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  font-weight: 600;
}

.result-table td {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  word-break: break-all;
}

.result-table tr:nth-child(even) {
  background-color: #fafafa;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.action-grid .btn {
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-grid .btn:hover {
  transform: translateY(-2px);
}

.btn-warning {
  background: #ff9800;
  color: #fff;
}

.btn-info {
  background: #03a9f4;
  color: #fff;
}

.btn-success {
  background: #4caf50;
  color: #fff;
}

.atomic-email-container {
  margin-top: 1rem;
}

.email-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid #e9ecef;
  box-shadow: none;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: none;
  border: 1px solid #f1f3f5;
}

.detail-label {
  font-weight: 500;
  min-width: 120px;
  color: #333;
  font-family: 'Google Sans', -apple-system, sans-serif;
}

.detail-value {
  flex: 1;
  font-family: 'Google Sans', -apple-system, sans-serif;
  word-break: break-all;
  padding: 0.25rem 0.5rem;
  background-color: #f1f3f5;
  border-radius: 4px;
  font-size: 0.9rem;
}

.copy-btn {
  background: none;
  border: none;
  color: #0066cc;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
  margin-left: 0.5rem;
}

.copy-btn:hover {
  background-color: #e9ecef;
}

.verification-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #fff8e1;
  border-radius: 8px;
  margin-top: 1rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 202, 40, 0.3);
  border-radius: 50%;
  border-top-color: #ffca28;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.hidden {
  display: none;
}

.mt-4 {
  margin-top: 1rem;
}

.verification-code-container {
  border-top: 1px solid #e9ecef;
  padding-top: 0.75rem;
  margin-top: 0.75rem;
  background-color: #e8f5e9;
  box-shadow: none;
}

#verification-code {
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  font-weight: 500;
  color: #1b5e20;
  background-color: #c8e6c9;
  font-family: 'Google Sans', -apple-system, sans-serif;
}

.copied {
  animation: copy-animation 1.5s ease;
}

@keyframes copy-animation {
  0% { background-color: #c8e6c9; }
  70% { background-color: #c8e6c9; }
  100% { background-color: transparent; }
}

.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.toast {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: toast-in 0.3s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px 15px;
  flex: 1;
}

.toast-content i {
  margin-top: 3px;
}

.toast-content span {
  font-size: 14px;
  color: #333;
}

.toast-close {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 8px 12px;
  transition: all 0.2s;
}

.toast-close:hover {
  color: #333;
}

.toast-error i {
  color: #f44336;
}

.toast-success i {
  color: #4caf50;
}

.toast-info i {
  color: #2196f3;
}

.toast-warning i {
  color: #ff9800;
}

.toast-closing {
  animation: toast-out 0.3s ease forwards;
}

@keyframes toast-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toast-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(20px);
  }
}

.rotating {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  position: relative;
}

.rotating i {
  position: absolute;
  animation: spin 1s linear infinite;
  transform-origin: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.email-generator-container {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--light-bg);
  border-radius: var(--border-radius);
  text-align: center;
}

.email-generator-container p {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.email-generator-container p:last-child {
  margin-bottom: 0;
  font-size: 0.9rem;
  opacity: 0.8;
} 
